#!/usr/bin/env python3
"""
Test script to verify that the data mixing fixes work correctly.
This script tests the isolation between different symbols and parameters.
"""

import sys
import os
import threading
import time
import json
from unittest.mock import Mock, patch

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'core', 'fyers'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from core.fyers.processor import (
    process_live_data, 
    clear_processor_state, 
    validate_live_data_message,
    _candle_state,
    _candle_state_lock
)

def test_data_validation():
    """Test the data validation function"""
    print("Testing data validation...")
    
    # Test valid message
    valid_msg = {
        'symbol': 'NSE:NIFTY25AUGFUT',
        'ltp': 25000.50,
        'exch_feed_time': *************
    }
    is_valid, msg = validate_live_data_message(valid_msg)
    assert is_valid, f"Valid message failed validation: {msg}"
    
    # Test invalid messages
    invalid_messages = [
        ({}, "Empty message"),
        ({'symbol': 'TEST'}, "Missing LTP"),
        ({'ltp': 100}, "Missing symbol"),
        ({'symbol': 'TEST', 'ltp': 0}, "Zero LTP"),
        ({'symbol': 'TEST', 'ltp': -100}, "Negative LTP"),
        ({'symbol': 'TEST', 'ltp': 'invalid'}, "Invalid LTP type"),
    ]
    
    for invalid_msg, description in invalid_messages:
        is_valid, _ = validate_live_data_message(invalid_msg)
        assert not is_valid, f"Invalid message passed validation: {description}"
    
    print("✓ Data validation tests passed")

def test_symbol_isolation():
    """Test that different symbols maintain separate state"""
    print("Testing symbol isolation...")

    # Clear any existing state
    with _candle_state_lock:
        _candle_state.clear()

    # Create test messages for different symbols
    symbol1_msg = {
        'symbol': 'NSE:NIFTY25AUGFUT',
        'ltp': 25000.0,
        'exch_feed_time': *************
    }

    symbol2_msg = {
        'symbol': 'NSE:BANKNIFTY25AUGFUT',
        'ltp': 50000.0,
        'exch_feed_time': *************
    }

    # Process data for both symbols
    print("Processing symbol 1...")
    result1 = process_live_data(symbol1_msg, '5m', 0.05, 100)
    print(f"Result 1: {result1}")

    print("Processing symbol 2...")
    result2 = process_live_data(symbol2_msg, '5m', 0.05, 100)
    print(f"Result 2: {result2}")

    # Verify both symbols have separate state
    assert result1 is not None, f"Symbol 1 processing failed: {result1}"
    assert result2 is not None, f"Symbol 2 processing failed: {result2}"
    assert result1['close'] == 25000.0, f"Symbol 1 data incorrect: {result1['close']}"
    assert result2['close'] == 50000.0, f"Symbol 2 data incorrect: {result2['close']}"

    # Verify separate keys in state
    with _candle_state_lock:
        keys = list(_candle_state.keys())
        print(f"State keys: {keys}")
        assert len(keys) == 2, f"Expected 2 state keys, got {len(keys)}"
        assert any('NIFTY25AUGFUT' in key for key in keys), "NIFTY key not found"
        assert any('BANKNIFTY25AUGFUT' in key for key in keys), "BANKNIFTY key not found"

    print("✓ Symbol isolation tests passed")

def test_parameter_isolation():
    """Test that different parameters maintain separate state"""
    print("Testing parameter isolation...")
    
    # Clear any existing state
    with _candle_state_lock:
        _candle_state.clear()
    
    symbol = 'NSE:NIFTY25AUGFUT'
    base_msg = {
        'symbol': symbol,
        'ltp': 25000.0,
        'exch_feed_time': *************
    }
    
    # Process with different parameters
    result1 = process_live_data(base_msg, '5m', 0.05, 100)
    result2 = process_live_data(base_msg, '1m', 0.05, 100)
    result3 = process_live_data(base_msg, '5m', 0.10, 100)
    result4 = process_live_data(base_msg, '5m', 0.05, 200)
    
    # Verify all combinations create separate state
    assert all(r is not None for r in [result1, result2, result3, result4]), "Some processing failed"
    
    # Verify separate keys in state
    with _candle_state_lock:
        keys = list(_candle_state.keys())
        assert len(keys) == 4, f"Expected 4 state keys, got {len(keys)}"
        
        expected_keys = [
            f"{symbol}_5m_0.05_100",
            f"{symbol}_1m_0.05_100", 
            f"{symbol}_5m_0.1_100",
            f"{symbol}_5m_0.05_200"
        ]
        
        for expected_key in expected_keys:
            assert expected_key in keys, f"Expected key {expected_key} not found in {keys}"
    
    print("✓ Parameter isolation tests passed")

def test_state_cleanup():
    """Test that state cleanup works correctly"""
    print("Testing state cleanup...")
    
    # Clear any existing state
    with _candle_state_lock:
        _candle_state.clear()
    
    symbol = 'NSE:NIFTY25AUGFUT'
    timeframe = '5m'
    bucket_size = 0.05
    multiplier = 100
    
    # Create some state
    msg = {
        'symbol': symbol,
        'ltp': 25000.0,
        'exch_feed_time': *************
    }
    
    result = process_live_data(msg, timeframe, bucket_size, multiplier)
    assert result is not None, "Initial processing failed"
    
    # Verify state exists
    key = f"{symbol}_{timeframe}_{bucket_size}_{multiplier}"
    with _candle_state_lock:
        assert key in _candle_state, "State not created"
    
    # Clear the state
    success = clear_processor_state(symbol, timeframe, bucket_size, multiplier)
    assert success, "State cleanup failed"
    
    # Verify state is cleared
    with _candle_state_lock:
        assert key not in _candle_state, "State not cleared"
    
    print("✓ State cleanup tests passed")

def test_thread_safety():
    """Test thread safety of the processor"""
    print("Testing thread safety...")
    
    # Clear any existing state
    with _candle_state_lock:
        _candle_state.clear()
    
    results = []
    errors = []
    
    def worker(worker_id):
        try:
            symbol = f'NSE:TEST{worker_id}'
            for i in range(10):
                msg = {
                    'symbol': symbol,
                    'ltp': 25000.0 + i,
                    'exch_feed_time': ************* + i
                }
                result = process_live_data(msg, '5m', 0.05, 100)
                if result:
                    results.append((worker_id, result))
                time.sleep(0.001)  # Small delay to encourage race conditions
        except Exception as e:
            errors.append((worker_id, str(e)))
    
    # Start multiple threads
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Check results
    assert len(errors) == 0, f"Thread safety errors: {errors}"
    assert len(results) > 0, "No results from threaded processing"
    
    # Verify separate state for each symbol
    with _candle_state_lock:
        keys = list(_candle_state.keys())
        unique_symbols = set(key.split('_')[0] for key in keys)
        assert len(unique_symbols) == 5, f"Expected 5 unique symbols, got {len(unique_symbols)}"
    
    print("✓ Thread safety tests passed")

def run_all_tests():
    """Run all tests"""
    print("Running data mixing fix tests...\n")
    
    try:
        test_data_validation()
        test_symbol_isolation()
        test_parameter_isolation()
        test_state_cleanup()
        test_thread_safety()
        
        print("\n✅ All tests passed! Data mixing fixes are working correctly.")
        return True
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        return False
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
