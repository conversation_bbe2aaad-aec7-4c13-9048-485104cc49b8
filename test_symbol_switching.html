<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Symbol Switching Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button.danger {
            background-color: #dc3545;
        }
        .test-button.danger:hover {
            background-color: #c82333;
        }
        .test-button.success {
            background-color: #28a745;
        }
        .test-button.success:hover {
            background-color: #218838;
        }
        .log-container {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .status-pending {
            background-color: #ffc107;
        }
        .chart-info {
            background-color: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .symbol-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 Live Data Symbol Switching Test</h1>
        
        <div class="test-section">
            <h2>Connection Status</h2>
            <div id="connection-status">
                <span class="status-indicator status-disconnected"></span>
                <span id="status-text">Disconnected</span>
            </div>
        </div>

        <div class="test-section">
            <h2>Test Scenarios</h2>
            <p>Use these buttons to test different symbol switching scenarios:</p>
            
            <div class="symbol-buttons">
                <button class="test-button" onclick="testSymbolSwitch('NSE:NIFTY25AUGFUT')">
                    Switch to NIFTY
                </button>
                <button class="test-button" onclick="testSymbolSwitch('NSE:BANKNIFTY25AUGFUT')">
                    Switch to BANKNIFTY
                </button>
                <button class="test-button" onclick="testSymbolSwitch('NSE:FINNIFTY25AUGFUT')">
                    Switch to FINNIFTY
                </button>
                <button class="test-button" onclick="testTimeframeSwitch('1m')">
                    Switch to 1m
                </button>
                <button class="test-button" onclick="testTimeframeSwitch('5m')">
                    Switch to 5m
                </button>
                <button class="test-button" onclick="testTimeframeSwitch('15m')">
                    Switch to 15m
                </button>
            </div>

            <div class="symbol-buttons">
                <button class="test-button success" onclick="testRapidSwitching()">
                    🚀 Rapid Switching Test
                </button>
                <button class="test-button danger" onclick="clearLogs()">
                    🧹 Clear Logs
                </button>
                <button class="test-button" onclick="showSubscriptionStatus()">
                    📊 Show Subscriptions
                </button>
            </div>
        </div>

        <div class="test-section">
            <h2>Current Chart Info</h2>
            <div id="chart-info" class="chart-info">
                <div>Chart ID: <span id="current-chart-id">N/A</span></div>
                <div>Symbol: <span id="current-symbol">N/A</span></div>
                <div>Timeframe: <span id="current-timeframe">N/A</span></div>
                <div>Last Update: <span id="last-update">N/A</span></div>
                <div>Live Data Count: <span id="live-data-count">0</span></div>
            </div>
        </div>

        <div class="test-section">
            <h2>Live Data Log</h2>
            <div id="log-container" class="log-container"></div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        let liveDataCount = 0;
        let currentSymbol = 'NSE:NIFTY25AUGFUT';
        let currentTimeframe = '5m';
        let currentChartId = 'test-chart-1';

        function initializeSocket() {
            if (socket) return socket;
            
            socket = io();
            
            socket.on('connect', () => {
                log('✅ Connected to server');
                updateConnectionStatus(true);
            });
            
            socket.on('disconnect', () => {
                log('❌ Disconnected from server');
                updateConnectionStatus(false);
            });
            
            socket.on('live_data_update', (data) => {
                liveDataCount++;
                log(`📊 Live data: ${data.symbol}-${data.timeframe} @ ${data.data.close} (Chart: ${data.chart_id})`);
                updateChartInfo(data);
            });
            
            socket.on('subscription_success', (data) => {
                log(`🔔 Subscription confirmed: ${data.symbol}-${data.timeframe} for ${data.chart_id}`);
            });
            
            socket.on('unsubscription_success', (data) => {
                log(`🔕 Unsubscription confirmed: ${data.symbol} for ${data.chart_id}`);
            });
            
            socket.on('error', (data) => {
                log(`❌ Error: ${data.message}`);
            });
            
            return socket;
        }

        function log(message) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            const indicator = document.querySelector('.status-indicator');
            const statusText = document.getElementById('status-text');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                statusText.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Disconnected';
            }
        }

        function updateChartInfo(data) {
            document.getElementById('current-chart-id').textContent = data.chart_id;
            document.getElementById('current-symbol').textContent = data.symbol;
            document.getElementById('current-timeframe').textContent = data.timeframe;
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
            document.getElementById('live-data-count').textContent = liveDataCount;
        }

        function testSymbolSwitch(symbol) {
            log(`🔄 Testing symbol switch to: ${symbol}`);
            
            // Unsubscribe from current
            if (socket) {
                socket.emit('unsubscribe_symbol', {
                    symbol: currentSymbol,
                    chart_id: currentChartId
                });
            }
            
            // Subscribe to new symbol
            currentSymbol = symbol;
            setTimeout(() => {
                if (socket) {
                    socket.emit('subscribe_symbol', {
                        symbol: symbol,
                        timeframe: currentTimeframe,
                        bucket_size: 0.05,
                        multiplier: 100,
                        chart_id: currentChartId
                    });
                }
            }, 100);
        }

        function testTimeframeSwitch(timeframe) {
            log(`🔄 Testing timeframe switch to: ${timeframe}`);
            
            // Unsubscribe from current
            if (socket) {
                socket.emit('unsubscribe_symbol', {
                    symbol: currentSymbol,
                    chart_id: currentChartId
                });
            }
            
            // Subscribe with new timeframe
            currentTimeframe = timeframe;
            setTimeout(() => {
                if (socket) {
                    socket.emit('subscribe_symbol', {
                        symbol: currentSymbol,
                        timeframe: timeframe,
                        bucket_size: 0.05,
                        multiplier: 100,
                        chart_id: currentChartId
                    });
                }
            }, 100);
        }

        function testRapidSwitching() {
            log('🚀 Starting rapid switching test...');
            const symbols = ['NSE:NIFTY25AUGFUT', 'NSE:BANKNIFTY25AUGFUT', 'NSE:FINNIFTY25AUGFUT'];
            let index = 0;
            
            const interval = setInterval(() => {
                if (index >= symbols.length * 3) {
                    clearInterval(interval);
                    log('✅ Rapid switching test completed');
                    return;
                }
                
                const symbol = symbols[index % symbols.length];
                testSymbolSwitch(symbol);
                index++;
            }, 1000);
        }

        function showSubscriptionStatus() {
            log('📊 Current subscription status:');
            log(`   Chart ID: ${currentChartId}`);
            log(`   Symbol: ${currentSymbol}`);
            log(`   Timeframe: ${currentTimeframe}`);
            log(`   Live data received: ${liveDataCount}`);
        }

        function clearLogs() {
            document.getElementById('log-container').innerHTML = '';
            liveDataCount = 0;
            document.getElementById('live-data-count').textContent = '0';
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            initializeSocket();
            log('🚀 Symbol switching test initialized');
            
            // Start with initial subscription
            setTimeout(() => {
                testSymbolSwitch(currentSymbol);
            }, 1000);
        });
    </script>
</body>
</html>
